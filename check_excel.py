#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看Excel文件内容
"""

import pandas as pd

def check_excel_file():
    """查看Excel文件内容"""
    filename = '组合分析详细数据_20250729_210431.xlsx'
    
    print("=== Excel文件内容预览 ===")
    
    # 读取Excel文件
    xl = pd.ExcelFile(filename)
    print(f"工作表: {xl.sheet_names}")
    
    for sheet in xl.sheet_names:
        print(f"\n--- {sheet} ---")
        df = pd.read_excel(filename, sheet_name=sheet)
        print(f"行数: {len(df)}, 列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")
        
        if len(df) > 0:
            print("前3行:")
            if '组合名称' in df.columns:
                # 显示关键列
                key_cols = ['组合名称', '维度数', '总人数', '转化人数', '转化率']
                available_cols = [col for col in key_cols if col in df.columns]
                print(df[available_cols].head(3).to_string(index=False))
            else:
                print(df.head(3).to_string(index=False))

if __name__ == "__main__":
    check_excel_file()
