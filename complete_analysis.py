#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的用户转化率分析系统
一键生成所有分析结果：期数分析、单维度分析、具体标签组合分析
"""

import pandas as pd
import numpy as np
from itertools import combinations
from datetime import datetime
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

class CompleteAnalyzer:
    def __init__(self, filename):
        self.filename = filename
        self.df = None
        self.conversion_threshold = 1000
        self.amount_col = '总金额（自动计算）【总金额大于1000为转化】'
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 定义分析维度
        self.dimensions = {
            '工作阶段': '📗工作阶段',
            '工作状态': '📗工作状态', 
            '心力评分': '心力评分_group',
            '工作年限': '📗工作年限',
            '薪资区间': '📗当前薪资区间',
            '投入成本': '📗在过去半年内，您在自我能力提升上投入了多少成本？',
            '城市类别': '城市类别_clean'
        }
        
    def load_and_preprocess_data(self):
        """加载和预处理数据"""
        print("正在加载数据...")
        self.df = pd.read_excel(self.filename)
        
        print("正在预处理数据...")
        # 清理城市类别
        self.df['城市类别_clean'] = self.df['城市类别'].apply(self._clean_city_category)
        
        # 分组心力评分
        self.df['心力评分_group'] = self.df['📗心力评分'].apply(self._group_score)
        
        # 创建转化标识
        self.df['is_converted'] = (self.df[self.amount_col] > self.conversion_threshold).astype(int)
        
        print(f"数据加载完成: {len(self.df)} 条记录")
        return self.df
    
    def _clean_city_category(self, city):
        """清理城市类别"""
        if pd.isna(city):
            return '未知'
        city_str = str(city)
        if '一线' in city_str:
            return '一线城市'
        elif '新一线' in city_str or '二线' in city_str:
            return '二线城市'
        elif '三线' in city_str or '四线' in city_str or '五线' in city_str:
            return '三线城市及以下'
        elif '海外' in city_str:
            return '海外'
        else:
            return '其他'
    
    def _group_score(self, score):
        """分组心力评分"""
        if pd.isna(score):
            return '未知'
        if score <= 3:
            return '低分(0-3)'
        elif score <= 6:
            return '中分(4-6)'
        elif score <= 8:
            return '高分(7-8)'
        else:
            return '极高分(9-10)'
    
    def analyze_by_period(self):
        """按期分析"""
        print("\n=== 开始按期分析 ===")
        
        periods = sorted(self.df['🌝期数'].unique())
        period_results = []
        
        for period in periods:
            period_data = self.df[self.df['🌝期数'] == period]
            
            # 基础统计
            total_users = len(period_data)
            converted_users = len(period_data[period_data['is_converted'] == 1])
            conversion_rate = converted_users / total_users if total_users > 0 else 0
            avg_amount = period_data[period_data['is_converted'] == 1][self.amount_col].mean()
            
            period_results.append({
                '期数': period,
                '总人数': total_users,
                '转化人数': converted_users,
                '转化率': f"{conversion_rate:.1%}",
                '转化用户客单价': f"{avg_amount:.0f}" if not pd.isna(avg_amount) else "0"
            })
            
            print(f"第{period}期: 总人数{total_users}, 转化人数{converted_users}, 转化率{conversion_rate:.1%}")
        
        # 导出期数分析结果
        period_df = pd.DataFrame(period_results)
        period_filename = f"各期转化率分析_{self.timestamp}.xlsx"
        period_df.to_excel(period_filename, index=False)
        print(f"✅ 各期分析结果已保存: {period_filename}")
        
        return period_results
    
    def analyze_single_dimension(self):
        """单维度分析"""
        print("\n=== 开始单维度分析 ===")
        
        single_results = {}
        
        for dim_name, col_name in self.dimensions.items():
            print(f"\n--- {dim_name} ---")
            
            # 计算该维度的转化率
            stats = self.df.groupby(col_name).agg({
                'is_converted': ['count', 'sum', 'mean'],
                self.amount_col: lambda x: x[x > self.conversion_threshold].mean()
            }).round(4)
            
            stats.columns = ['总人数', '转化人数', '转化率', '转化用户客单价']
            stats['转化率_百分比'] = (stats['转化率'] * 100).round(1)
            stats = stats.sort_values('转化率', ascending=False)
            
            # 显示结果
            for idx, row in stats.iterrows():
                print(f"{idx}: {row['转化率_百分比']:.1f}% ({row['总人数']}人)")
            
            single_results[dim_name] = stats
        
        # 导出单维度分析结果
        single_filename = f"单维度转化率分析_{self.timestamp}.xlsx"
        with pd.ExcelWriter(single_filename, engine='openpyxl') as writer:
            for dim_name, data in single_results.items():
                data.to_excel(writer, sheet_name=dim_name)
        
        print(f"✅ 单维度分析结果已保存: {single_filename}")
        return single_results

    def analyze_specific_combinations(self, min_sample_size=30):
        """分析具体标签组合"""
        print("\n=== 开始具体标签组合分析 ===")

        all_combinations = []

        # 生成2-4维组合
        dimension_list = list(self.dimensions.items())

        for r in range(2, 5):  # 2-4维组合
            print(f"\n--- {r}维组合分析 ---")

            for combo in combinations(dimension_list, r):
                combo_names = [name for name, _ in combo]
                combo_cols = [col for _, col in combo]

                # 按组合分组计算转化率
                grouped = self.df.groupby(combo_cols).agg({
                    'is_converted': ['count', 'sum', 'mean'],
                    self.amount_col: lambda x: x[x > self.conversion_threshold].mean()
                })

                grouped.columns = ['总人数', '转化人数', '转化率', '转化用户客单价']

                # 过滤样本量
                grouped = grouped[grouped['总人数'] >= min_sample_size]

                if len(grouped) == 0:
                    continue

                # 为每个组合创建具体标签描述
                for idx, row in grouped.iterrows():
                    if isinstance(idx, tuple):
                        # 多维组合
                        specific_combo = ' × '.join([f"{combo_names[i]}:{idx[i]}" for i in range(len(idx))])
                    else:
                        # 单维（不应该出现在这里，但防御性编程）
                        specific_combo = f"{combo_names[0]}:{idx}"

                    all_combinations.append({
                        '维度数': r,
                        '具体组合': specific_combo,
                        '总人数': int(row['总人数']),
                        '转化人数': int(row['转化人数']),
                        '转化率': row['转化率'],
                        '转化率_百分比': round(row['转化率'] * 100, 2),
                        '转化用户客单价': round(row['转化用户客单价'], 0) if not pd.isna(row['转化用户客单价']) else 0
                    })

        # 转换为DataFrame
        combo_df = pd.DataFrame(all_combinations)

        if len(combo_df) == 0:
            print("❌ 没有找到符合条件的组合")
            return None

        # 计算转化率阈值
        overall_rate = self.df['is_converted'].mean()
        std_rate = combo_df['转化率'].std()
        high_threshold = overall_rate + std_rate
        low_threshold = overall_rate - std_rate

        print(f"总体转化率: {overall_rate:.1%}")
        print(f"高转化率阈值: {high_threshold:.1%}")
        print(f"低转化率阈值: {low_threshold:.1%}")

        # 分类组合
        high_conversion = combo_df[combo_df['转化率'] > high_threshold].copy()
        high_conversion = high_conversion.sort_values('转化率', ascending=False)

        low_conversion = combo_df[combo_df['转化率'] < low_threshold].copy()
        low_conversion = low_conversion.sort_values('转化率', ascending=True)

        medium_conversion = combo_df[
            (combo_df['转化率'] >= low_threshold) &
            (combo_df['转化率'] <= high_threshold)
        ].copy()
        medium_conversion = medium_conversion.sort_values(['总人数', '转化率'], ascending=[False, False])

        # 显示结果
        print(f"\n🎯 高转化率组合 ({len(high_conversion)}个):")
        for _, row in high_conversion.head(10).iterrows():
            print(f"  {row['具体组合']}: {row['转化率_百分比']:.1f}% ({row['总人数']}人)")

        print(f"\n📉 低转化率组合 ({len(low_conversion)}个):")
        for _, row in low_conversion.head(10).iterrows():
            print(f"  {row['具体组合']}: {row['转化率_百分比']:.1f}% ({row['总人数']}人)")

        print(f"\n📊 中等转化率组合 ({len(medium_conversion)}个):")
        for _, row in medium_conversion.head(10).iterrows():
            print(f"  {row['具体组合']}: {row['转化率_百分比']:.1f}% ({row['总人数']}人)")

        # 导出具体组合分析结果
        combo_filename = f"具体标签组合分析_{self.timestamp}.xlsx"
        with pd.ExcelWriter(combo_filename, engine='openpyxl') as writer:
            high_conversion.to_excel(writer, sheet_name='高转化率组合', index=False)
            medium_conversion.to_excel(writer, sheet_name='中等转化率组合', index=False)
            low_conversion.to_excel(writer, sheet_name='低转化率组合', index=False)
            combo_df.to_excel(writer, sheet_name='所有组合', index=False)

        print(f"✅ 具体标签组合分析结果已保存: {combo_filename}")

        return {
            'high_conversion': high_conversion,
            'medium_conversion': medium_conversion,
            'low_conversion': low_conversion,
            'all_combinations': combo_df
        }

    def generate_summary_report(self):
        """生成总结报告"""
        print("\n" + "="*80)
        print("📊 完整转化率分析总结报告")
        print("="*80)

        # 基础数据概览
        total_users = len(self.df)
        converted_users = len(self.df[self.df['is_converted'] == 1])
        overall_rate = converted_users / total_users
        avg_amount = self.df[self.df['is_converted'] == 1][self.amount_col].mean()

        print(f"\n📈 基础数据概览:")
        print(f"   • 总用户数: {total_users:,}")
        print(f"   • 转化用户数: {converted_users:,}")
        print(f"   • 总转化率: {overall_rate:.2%}")
        print(f"   • 转化用户平均客单价: {avg_amount:.2f}元")

        # 各期转化率范围
        period_rates = []
        for period in sorted(self.df['🌝期数'].unique()):
            period_data = self.df[self.df['🌝期数'] == period]
            period_rate = period_data['is_converted'].mean()
            period_rates.append((period, period_rate))

        period_rates.sort(key=lambda x: x[1])
        print(f"\n📅 各期转化率范围:")
        print(f"   • 转化率最高: 第{period_rates[-1][0]}期 ({period_rates[-1][1]:.2%})")
        print(f"   • 转化率最低: 第{period_rates[0][0]}期 ({period_rates[0][1]:.2%})")

        # 单维度关键发现
        print(f"\n🎯 单维度关键发现:")
        for dim_name, col_name in self.dimensions.items():
            top_category = self.df.groupby(col_name)['is_converted'].mean().sort_values(ascending=False).head(1)
            if len(top_category) > 0:
                category = top_category.index[0]
                rate = top_category.iloc[0]
                print(f"   • {dim_name}最高转化率: {category} ({rate:.1%})")

        print(f"\n✅ 分析完成！所有结果文件已生成到当前目录")
        print(f"   • 时间戳: {self.timestamp}")

    def generate_html_report(self, period_results, single_results, combo_results):
        """生成HTML可视化报告"""
        print("\n=== 开始生成HTML可视化报告 ===")

        # 创建图表
        charts = {}

        # 1. 各期转化率趋势图
        period_df = pd.DataFrame(period_results)
        period_df['转化率_数值'] = period_df['转化率'].str.rstrip('%').astype(float)

        fig_trend = px.line(period_df, x='期数', y='转化率_数值',
                           title='各期转化率趋势',
                           labels={'转化率_数值': '转化率(%)', '期数': '期数'})
        fig_trend.update_traces(mode='lines+markers')
        charts['trend'] = fig_trend.to_html(include_plotlyjs='cdn')

        # 2. 单维度转化率对比图
        fig_single = make_subplots(
            rows=2, cols=2,
            subplot_titles=('工作阶段转化率', '薪资区间转化率', '投入成本转化率', '城市类别转化率'),
            specs=[[{"type": "bar"}, {"type": "bar"}],
                   [{"type": "bar"}, {"type": "bar"}]]
        )

        # 工作阶段
        stage_data = single_results['工作阶段'].reset_index()
        fig_single.add_trace(
            go.Bar(x=stage_data.index, y=stage_data['转化率_百分比'],
                   name='工作阶段', text=stage_data['转化率_百分比']),
            row=1, col=1
        )

        # 薪资区间
        salary_data = single_results['薪资区间'].reset_index()
        fig_single.add_trace(
            go.Bar(x=salary_data.index, y=salary_data['转化率_百分比'],
                   name='薪资区间', text=salary_data['转化率_百分比']),
            row=1, col=2
        )

        # 投入成本
        cost_data = single_results['投入成本'].reset_index()
        fig_single.add_trace(
            go.Bar(x=cost_data.index, y=cost_data['转化率_百分比'],
                   name='投入成本', text=cost_data['转化率_百分比']),
            row=2, col=1
        )

        # 城市类别
        city_data = single_results['城市类别'].reset_index()
        fig_single.add_trace(
            go.Bar(x=city_data.index, y=city_data['转化率_百分比'],
                   name='城市类别', text=city_data['转化率_百分比']),
            row=2, col=2
        )

        fig_single.update_layout(height=800, showlegend=False, title_text="单维度转化率对比")
        charts['single_dimension'] = fig_single.to_html(include_plotlyjs='cdn')

        # 3. 高转化率组合分析图
        if combo_results and len(combo_results['high_conversion']) > 0:
            high_combo = combo_results['high_conversion'].head(20)

            fig_combo = px.bar(
                high_combo,
                x='转化率_百分比',
                y='具体组合',
                orientation='h',
                title='Top 20 高转化率组合',
                labels={'转化率_百分比': '转化率(%)', '具体组合': '组合'},
                text='转化率_百分比'
            )
            fig_combo.update_layout(height=800, yaxis={'categoryorder': 'total ascending'})
            charts['high_combinations'] = fig_combo.to_html(include_plotlyjs='cdn')
        else:
            charts['high_combinations'] = "<p>暂无高转化率组合数据</p>"

        # 4. 转化率分布直方图
        if combo_results:
            all_combos = combo_results['all_combinations']
            fig_dist = px.histogram(
                all_combos,
                x='转化率_百分比',
                nbins=50,
                title='所有组合转化率分布',
                labels={'转化率_百分比': '转化率(%)', 'count': '组合数量'}
            )
            charts['distribution'] = fig_dist.to_html(include_plotlyjs='cdn')
        else:
            charts['distribution'] = "<p>暂无组合分析数据</p>"

        # 生成HTML报告
        html_content = self._create_html_template(charts, period_results, single_results, combo_results)

        html_filename = f"完整转化率分析报告_{self.timestamp}.html"
        with open(html_filename, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"✅ HTML可视化报告已生成: {html_filename}")
        return html_filename

    def _create_html_template(self, charts, period_results, single_results, combo_results):
        """创建HTML模板"""

        # 基础统计
        total_users = len(self.df)
        converted_users = len(self.df[self.df['is_converted'] == 1])
        overall_rate = converted_users / total_users
        avg_amount = self.df[self.df['is_converted'] == 1][self.amount_col].mean()

        # 高转化率组合表格
        high_combo_table = ""
        if combo_results and len(combo_results['high_conversion']) > 0:
            high_combo = combo_results['high_conversion'].head(10)
            high_combo_table = high_combo[['具体组合', '转化率_百分比', '总人数', '转化人数']].to_html(
                classes='table table-striped',
                index=False,
                escape=False
            )

        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整转化率分析报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }}
        .header h1 {{
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }}
        .header .subtitle {{
            color: #666;
            font-size: 1.2em;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }}
        .stat-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .stat-card h3 {{
            margin: 0 0 10px 0;
            font-size: 1.1em;
            opacity: 0.9;
        }}
        .stat-card .value {{
            font-size: 2em;
            font-weight: bold;
            margin: 0;
        }}
        .section {{
            margin-bottom: 50px;
        }}
        .section h2 {{
            color: #333;
            border-left: 5px solid #007bff;
            padding-left: 15px;
            margin-bottom: 25px;
            font-size: 1.8em;
        }}
        .chart-container {{
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }}
        .table-container {{
            overflow-x: auto;
            margin-bottom: 30px;
        }}
        .table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }}
        .table th, .table td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        .table th {{
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }}
        .table-striped tbody tr:nth-child(odd) {{
            background-color: #f9f9f9;
        }}
        .table tbody tr:hover {{
            background-color: #e8f4f8;
        }}
        .footer {{
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 完整转化率分析报告</h1>
            <div class="subtitle">生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <h3>总用户数</h3>
                <div class="value">{total_users:,}</div>
            </div>
            <div class="stat-card">
                <h3>转化用户数</h3>
                <div class="value">{converted_users:,}</div>
            </div>
            <div class="stat-card">
                <h3>总转化率</h3>
                <div class="value">{overall_rate:.2%}</div>
            </div>
            <div class="stat-card">
                <h3>平均客单价</h3>
                <div class="value">{avg_amount:.0f}元</div>
            </div>
        </div>

        <div class="section">
            <h2>📈 各期转化率趋势</h2>
            <div class="chart-container">
                {charts['trend']}
            </div>
        </div>

        <div class="section">
            <h2>📊 单维度转化率对比</h2>
            <div class="chart-container">
                {charts['single_dimension']}
            </div>
        </div>

        <div class="section">
            <h2>🎯 高转化率组合分析</h2>
            <div class="chart-container">
                {charts['high_combinations']}
            </div>

            <h3>📋 Top 10 高转化率组合详情</h3>
            <div class="table-container">
                {high_combo_table}
            </div>
        </div>

        <div class="section">
            <h2>📈 转化率分布分析</h2>
            <div class="chart-container">
                {charts['distribution']}
            </div>
        </div>

        <div class="footer">
            <p>📊 数据分析系统 | 生成时间: {self.timestamp}</p>
        </div>
    </div>
</body>
</html>
        """

        return html_template

def main():
    """主函数"""
    print("🚀 启动完整转化率分析系统")
    print("="*50)

    # 创建分析器
    analyzer = CompleteAnalyzer('用户信息表.xlsx')

    # 1. 加载数据
    analyzer.load_and_preprocess_data()

    # 2. 按期分析
    period_results = analyzer.analyze_by_period()

    # 3. 单维度分析
    single_results = analyzer.analyze_single_dimension()

    # 4. 具体标签组合分析
    combo_results = analyzer.analyze_specific_combinations(min_sample_size=30)

    # 5. 生成HTML可视化报告
    html_filename = analyzer.generate_html_report(period_results, single_results, combo_results)

    # 6. 生成总结报告
    analyzer.generate_summary_report()

    print(f"\n🎉 所有分析完成！生成的文件包括:")
    print(f"   📊 Excel表格: 各期分析、单维度分析、具体标签组合分析")
    print(f"   🌐 HTML报告: {html_filename}")
    print(f"   📝 控制台报告: 实时分析结果和总结")

if __name__ == "__main__":
    main()
