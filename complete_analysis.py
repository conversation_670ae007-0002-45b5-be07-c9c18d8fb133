#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的用户转化率分析系统
一键生成所有分析结果：期数分析、单维度分析、具体标签组合分析
"""

import pandas as pd
import numpy as np
from itertools import combinations
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class CompleteAnalyzer:
    def __init__(self, filename):
        self.filename = filename
        self.df = None
        self.conversion_threshold = 1000
        self.amount_col = '总金额（自动计算）【总金额大于1000为转化】'
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 定义分析维度
        self.dimensions = {
            '工作阶段': '📗工作阶段',
            '工作状态': '📗工作状态', 
            '心力评分': '心力评分_group',
            '工作年限': '📗工作年限',
            '薪资区间': '📗当前薪资区间',
            '投入成本': '📗在过去半年内，您在自我能力提升上投入了多少成本？',
            '城市类别': '城市类别_clean'
        }
        
    def load_and_preprocess_data(self):
        """加载和预处理数据"""
        print("正在加载数据...")
        self.df = pd.read_excel(self.filename)
        
        print("正在预处理数据...")
        # 清理城市类别
        self.df['城市类别_clean'] = self.df['城市类别'].apply(self._clean_city_category)
        
        # 分组心力评分
        self.df['心力评分_group'] = self.df['📗心力评分'].apply(self._group_score)
        
        # 创建转化标识
        self.df['is_converted'] = (self.df[self.amount_col] > self.conversion_threshold).astype(int)
        
        print(f"数据加载完成: {len(self.df)} 条记录")
        return self.df
    
    def _clean_city_category(self, city):
        """清理城市类别"""
        if pd.isna(city):
            return '未知'
        city_str = str(city)
        if '一线' in city_str:
            return '一线城市'
        elif '新一线' in city_str or '二线' in city_str:
            return '二线城市'
        elif '三线' in city_str or '四线' in city_str or '五线' in city_str:
            return '三线城市及以下'
        elif '海外' in city_str:
            return '海外'
        else:
            return '其他'
    
    def _group_score(self, score):
        """分组心力评分"""
        if pd.isna(score):
            return '未知'
        if score <= 3:
            return '低分(0-3)'
        elif score <= 6:
            return '中分(4-6)'
        elif score <= 8:
            return '高分(7-8)'
        else:
            return '极高分(9-10)'
    
    def analyze_by_period(self):
        """按期分析"""
        print("\n=== 开始按期分析 ===")
        
        periods = sorted(self.df['🌝期数'].unique())
        period_results = []
        
        for period in periods:
            period_data = self.df[self.df['🌝期数'] == period]
            
            # 基础统计
            total_users = len(period_data)
            converted_users = len(period_data[period_data['is_converted'] == 1])
            conversion_rate = converted_users / total_users if total_users > 0 else 0
            avg_amount = period_data[period_data['is_converted'] == 1][self.amount_col].mean()
            
            period_results.append({
                '期数': period,
                '总人数': total_users,
                '转化人数': converted_users,
                '转化率': f"{conversion_rate:.1%}",
                '转化用户客单价': f"{avg_amount:.0f}" if not pd.isna(avg_amount) else "0"
            })
            
            print(f"第{period}期: 总人数{total_users}, 转化人数{converted_users}, 转化率{conversion_rate:.1%}")
        
        # 导出期数分析结果
        period_df = pd.DataFrame(period_results)
        period_filename = f"各期转化率分析_{self.timestamp}.xlsx"
        period_df.to_excel(period_filename, index=False)
        print(f"✅ 各期分析结果已保存: {period_filename}")
        
        return period_results
    
    def analyze_single_dimension(self):
        """单维度分析"""
        print("\n=== 开始单维度分析 ===")
        
        single_results = {}
        
        for dim_name, col_name in self.dimensions.items():
            print(f"\n--- {dim_name} ---")
            
            # 计算该维度的转化率
            stats = self.df.groupby(col_name).agg({
                'is_converted': ['count', 'sum', 'mean'],
                self.amount_col: lambda x: x[x > self.conversion_threshold].mean()
            }).round(4)
            
            stats.columns = ['总人数', '转化人数', '转化率', '转化用户客单价']
            stats['转化率_百分比'] = (stats['转化率'] * 100).round(1)
            stats = stats.sort_values('转化率', ascending=False)
            
            # 显示结果
            for idx, row in stats.iterrows():
                print(f"{idx}: {row['转化率_百分比']:.1f}% ({row['总人数']}人)")
            
            single_results[dim_name] = stats
        
        # 导出单维度分析结果
        single_filename = f"单维度转化率分析_{self.timestamp}.xlsx"
        with pd.ExcelWriter(single_filename, engine='openpyxl') as writer:
            for dim_name, data in single_results.items():
                data.to_excel(writer, sheet_name=dim_name)
        
        print(f"✅ 单维度分析结果已保存: {single_filename}")
        return single_results

    def analyze_specific_combinations(self, min_sample_size=30):
        """分析具体标签组合"""
        print("\n=== 开始具体标签组合分析 ===")

        all_combinations = []

        # 生成2-4维组合
        dimension_list = list(self.dimensions.items())

        for r in range(2, 5):  # 2-4维组合
            print(f"\n--- {r}维组合分析 ---")

            for combo in combinations(dimension_list, r):
                combo_names = [name for name, _ in combo]
                combo_cols = [col for _, col in combo]

                # 按组合分组计算转化率
                grouped = self.df.groupby(combo_cols).agg({
                    'is_converted': ['count', 'sum', 'mean'],
                    self.amount_col: lambda x: x[x > self.conversion_threshold].mean()
                })

                grouped.columns = ['总人数', '转化人数', '转化率', '转化用户客单价']

                # 过滤样本量
                grouped = grouped[grouped['总人数'] >= min_sample_size]

                if len(grouped) == 0:
                    continue

                # 为每个组合创建具体标签描述
                for idx, row in grouped.iterrows():
                    if isinstance(idx, tuple):
                        # 多维组合
                        specific_combo = ' × '.join([f"{combo_names[i]}:{idx[i]}" for i in range(len(idx))])
                    else:
                        # 单维（不应该出现在这里，但防御性编程）
                        specific_combo = f"{combo_names[0]}:{idx}"

                    all_combinations.append({
                        '维度数': r,
                        '具体组合': specific_combo,
                        '总人数': int(row['总人数']),
                        '转化人数': int(row['转化人数']),
                        '转化率': row['转化率'],
                        '转化率_百分比': round(row['转化率'] * 100, 2),
                        '转化用户客单价': round(row['转化用户客单价'], 0) if not pd.isna(row['转化用户客单价']) else 0
                    })

        # 转换为DataFrame
        combo_df = pd.DataFrame(all_combinations)

        if len(combo_df) == 0:
            print("❌ 没有找到符合条件的组合")
            return None

        # 计算转化率阈值
        overall_rate = self.df['is_converted'].mean()
        std_rate = combo_df['转化率'].std()
        high_threshold = overall_rate + std_rate
        low_threshold = overall_rate - std_rate

        print(f"总体转化率: {overall_rate:.1%}")
        print(f"高转化率阈值: {high_threshold:.1%}")
        print(f"低转化率阈值: {low_threshold:.1%}")

        # 分类组合
        high_conversion = combo_df[combo_df['转化率'] > high_threshold].copy()
        high_conversion = high_conversion.sort_values('转化率', ascending=False)

        low_conversion = combo_df[combo_df['转化率'] < low_threshold].copy()
        low_conversion = low_conversion.sort_values('转化率', ascending=True)

        medium_conversion = combo_df[
            (combo_df['转化率'] >= low_threshold) &
            (combo_df['转化率'] <= high_threshold)
        ].copy()
        medium_conversion = medium_conversion.sort_values(['总人数', '转化率'], ascending=[False, False])

        # 显示结果
        print(f"\n🎯 高转化率组合 ({len(high_conversion)}个):")
        for _, row in high_conversion.head(10).iterrows():
            print(f"  {row['具体组合']}: {row['转化率_百分比']:.1f}% ({row['总人数']}人)")

        print(f"\n📉 低转化率组合 ({len(low_conversion)}个):")
        for _, row in low_conversion.head(10).iterrows():
            print(f"  {row['具体组合']}: {row['转化率_百分比']:.1f}% ({row['总人数']}人)")

        print(f"\n📊 中等转化率组合 ({len(medium_conversion)}个):")
        for _, row in medium_conversion.head(10).iterrows():
            print(f"  {row['具体组合']}: {row['转化率_百分比']:.1f}% ({row['总人数']}人)")

        # 导出具体组合分析结果
        combo_filename = f"具体标签组合分析_{self.timestamp}.xlsx"
        with pd.ExcelWriter(combo_filename, engine='openpyxl') as writer:
            high_conversion.to_excel(writer, sheet_name='高转化率组合', index=False)
            medium_conversion.to_excel(writer, sheet_name='中等转化率组合', index=False)
            low_conversion.to_excel(writer, sheet_name='低转化率组合', index=False)
            combo_df.to_excel(writer, sheet_name='所有组合', index=False)

        print(f"✅ 具体标签组合分析结果已保存: {combo_filename}")

        return {
            'high_conversion': high_conversion,
            'medium_conversion': medium_conversion,
            'low_conversion': low_conversion,
            'all_combinations': combo_df
        }

    def generate_summary_report(self):
        """生成总结报告"""
        print("\n" + "="*80)
        print("📊 完整转化率分析总结报告")
        print("="*80)

        # 基础数据概览
        total_users = len(self.df)
        converted_users = len(self.df[self.df['is_converted'] == 1])
        overall_rate = converted_users / total_users
        avg_amount = self.df[self.df['is_converted'] == 1][self.amount_col].mean()

        print(f"\n📈 基础数据概览:")
        print(f"   • 总用户数: {total_users:,}")
        print(f"   • 转化用户数: {converted_users:,}")
        print(f"   • 总转化率: {overall_rate:.2%}")
        print(f"   • 转化用户平均客单价: {avg_amount:.2f}元")

        # 各期转化率范围
        period_rates = []
        for period in sorted(self.df['🌝期数'].unique()):
            period_data = self.df[self.df['🌝期数'] == period]
            period_rate = period_data['is_converted'].mean()
            period_rates.append((period, period_rate))

        period_rates.sort(key=lambda x: x[1])
        print(f"\n📅 各期转化率范围:")
        print(f"   • 转化率最高: 第{period_rates[-1][0]}期 ({period_rates[-1][1]:.2%})")
        print(f"   • 转化率最低: 第{period_rates[0][0]}期 ({period_rates[0][1]:.2%})")

        # 单维度关键发现
        print(f"\n🎯 单维度关键发现:")
        for dim_name, col_name in self.dimensions.items():
            top_category = self.df.groupby(col_name)['is_converted'].mean().sort_values(ascending=False).head(1)
            if len(top_category) > 0:
                category = top_category.index[0]
                rate = top_category.iloc[0]
                print(f"   • {dim_name}最高转化率: {category} ({rate:.1%})")

        print(f"\n✅ 分析完成！所有结果文件已生成到当前目录")
        print(f"   • 时间戳: {self.timestamp}")

def main():
    """主函数"""
    print("🚀 启动完整转化率分析系统")
    print("="*50)

    # 创建分析器
    analyzer = CompleteAnalyzer('用户信息表.xlsx')

    # 1. 加载数据
    analyzer.load_and_preprocess_data()

    # 2. 按期分析
    period_results = analyzer.analyze_by_period()

    # 3. 单维度分析
    single_results = analyzer.analyze_single_dimension()

    # 4. 具体标签组合分析
    combo_results = analyzer.analyze_specific_combinations(min_sample_size=30)

    # 5. 生成总结报告
    analyzer.generate_summary_report()

if __name__ == "__main__":
    main()
