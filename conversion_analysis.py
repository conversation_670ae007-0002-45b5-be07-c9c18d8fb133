#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户转化率分析系统
分析各个标签维度的转化率，识别高转化率组合
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from itertools import combinations
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ConversionAnalyzer:
    def __init__(self, excel_path):
        """初始化分析器"""
        self.excel_path = excel_path
        self.df = None
        self.amount_col = '总金额（自动计算）【总金额大于1000为转化】'
        self.conversion_threshold = 1000
        
        # 定义分析维度
        self.dimensions = {
            '工作阶段': '📗工作阶段',
            '工作状态': '📗工作状态', 
            '心力评分': '📗心力评分',
            '工作年限': '📗工作年限',
            '薪资区间': '📗当前薪资区间',
            '投入成本': '📗在过去半年内，您在自我能力提升上投入了多少成本？',
            '城市类别': '城市类别'
        }
        
    def load_and_preprocess_data(self):
        """加载和预处理数据"""
        print("正在加载数据...")
        self.df = pd.read_excel(self.excel_path)
        
        print("正在预处理数据...")
        # 处理城市类别
        self.df['城市类别_clean'] = self.df['城市类别'].apply(self._clean_city_category)
        self.dimensions['城市类别'] = '城市类别_clean'
        
        # 处理心力评分分组
        self.df['心力评分_group'] = self.df['📗心力评分'].apply(self._group_score)
        self.dimensions['心力评分'] = '心力评分_group'
        
        # 添加转化标识
        self.df['is_converted'] = (self.df[self.amount_col] > self.conversion_threshold).astype(int)
        
        print(f"数据加载完成: {len(self.df)} 条记录")
        return self.df
    
    def _clean_city_category(self, city_text):
        """清洗城市类别数据"""
        if pd.isna(city_text):
            return '未知'
        
        city_str = str(city_text)
        if '一线城市' in city_str:
            return '一线城市'
        elif '新一线城市' in city_str:
            return '新一线城市'
        elif '二线城市' in city_str:
            return '二线城市'
        elif '三线城市' in city_str:
            return '三线城市及以下'
        elif '海外' in city_str:
            return '海外'
        else:
            return '其他'
    
    def _group_score(self, score):
        """心力评分分组"""
        if pd.isna(score):
            return '未知'
        if score <= 3:
            return '低分(0-3)'
        elif score <= 6:
            return '中分(4-6)'
        elif score <= 8:
            return '高分(7-8)'
        else:
            return '极高分(9-10)'
    
    def analyze_single_dimension(self, period=None):
        """单维度分析"""
        if period:
            data = self.df[self.df['🌝期数'] == period].copy()
            print(f"\n=== {period} 单维度转化率分析 ===")
        else:
            data = self.df.copy()
            print(f"\n=== 全期汇总 单维度转化率分析 ===")
        
        results = {}
        
        for dim_name, col_name in self.dimensions.items():
            print(f"\n--- {dim_name} ---")
            
            # 计算各子标签的转化指标
            stats = data.groupby(col_name).agg({
                'is_converted': ['count', 'sum', 'mean'],
                self.amount_col: ['mean', lambda x: x[x > self.conversion_threshold].mean()]
            }).round(2)
            
            # 重命名列
            stats.columns = ['总人数', '转化人数', '转化率', '人均值', '转化用户客单价']
            stats['转化率'] = (stats['转化率'] * 100).round(2)
            
            # 按转化率排序
            stats = stats.sort_values('转化率', ascending=False)
            
            print(stats)
            results[dim_name] = stats
            
        return results
    
    def analyze_by_period(self):
        """按期数分析"""
        periods = sorted(self.df['🌝期数'].unique())
        period_results = {}
        
        for period in periods:
            period_results[period] = self.analyze_single_dimension(period)
            
        return period_results
    
    def analyze_combinations(self, min_sample_size=50, period=None):
        """多维度组合分析"""
        if period:
            data = self.df[self.df['🌝期数'] == period].copy()
            print(f"\n=== {period} 多维度组合分析 ===")
        else:
            data = self.df.copy()
            print(f"\n=== 全期汇总 多维度组合分析 ===")
        
        # 计算总体转化率作为基准
        overall_conversion_rate = data['is_converted'].mean()
        overall_std = data['is_converted'].std()
        
        high_threshold = overall_conversion_rate + overall_std
        low_threshold = max(0, overall_conversion_rate - overall_std)
        
        print(f"总体转化率: {overall_conversion_rate:.4f}")
        print(f"高转化率阈值: {high_threshold:.4f}")
        print(f"低转化率阈值: {low_threshold:.4f}")
        
        all_combinations = []
        
        # 分析不同维度的组合 - 扩展到4维
        for r in range(2, 5):  # 2-4维组合
            print(f"\n--- {r}维组合分析 ---")
            
            for combo in combinations(self.dimensions.items(), r):
                combo_name = " × ".join([item[0] for item in combo])
                combo_cols = [item[1] for item in combo]
                
                # 计算组合统计
                combo_stats = data.groupby(combo_cols).agg({
                    'is_converted': ['count', 'sum', 'mean'],
                    self.amount_col: ['mean', lambda x: x[x > self.conversion_threshold].mean()]
                }).round(4)
                
                combo_stats.columns = ['总人数', '转化人数', '转化率', '人均值', '转化用户客单价']
                
                # 过滤样本量太小的组合 - 对于高维组合降低最小样本要求
                min_size = min_sample_size if r <= 2 else max(20, min_sample_size // (r-1))
                combo_stats = combo_stats[combo_stats['总人数'] >= min_size]
                
                if len(combo_stats) > 0:
                    # 标记高/低转化率组合
                    combo_stats['类型'] = combo_stats['转化率'].apply(
                        lambda x: '高转化率' if x >= high_threshold 
                        else ('低转化率' if x <= low_threshold else '中等转化率')
                    )
                    
                    combo_stats['组合名称'] = combo_name
                    combo_stats['维度数'] = r
                    
                    all_combinations.append(combo_stats)
        
        if all_combinations:
            # 合并所有组合结果
            combined_results = pd.concat(all_combinations, ignore_index=True)

            # 分别展示高转化率和低转化率组合
            self._display_combination_results(combined_results, overall_conversion_rate)

            # 生成结构化的组合分析结果
            structured_results = self._generate_structured_combination_results(combined_results, overall_conversion_rate)

            return structured_results
        else:
            print("未找到符合条件的组合")
            return {
                'high_conversion': pd.DataFrame(),
                'low_conversion': pd.DataFrame(),
                'high_potential': pd.DataFrame(),
                'all_combinations': pd.DataFrame()
            }
    
    def _display_combination_results(self, results, overall_rate):
        """展示组合分析结果"""
        print(f"\n=== 高转化率组合 (转化率 > {overall_rate + results.groupby('组合名称')['转化率'].std().mean():.4f}) ===")
        high_conv = results[results['类型'] == '高转化率'].sort_values('转化率', ascending=False)
        if len(high_conv) > 0:
            print(high_conv[['组合名称', '总人数', '转化人数', '转化率', '转化用户客单价']].head(10))
        else:
            print("未发现高转化率组合")
        
        print(f"\n=== 低转化率组合 ===")
        low_conv = results[results['类型'] == '低转化率'].sort_values('转化率', ascending=True)
        if len(low_conv) > 0:
            print(low_conv[['组合名称', '总人数', '转化人数', '转化率', '转化用户客单价']].head(10))
        else:
            print("未发现低转化率组合")
        
        print(f"\n=== 高潜力组合 (人数多且转化率中等偏上) ===")
        potential = results[
            (results['类型'] == '中等转化率') & 
            (results['总人数'] >= results['总人数'].quantile(0.7)) &
            (results['转化率'] >= overall_rate)
        ].sort_values(['总人数', '转化率'], ascending=[False, False])
        
        if len(potential) > 0:
            print(potential[['组合名称', '总人数', '转化人数', '转化率', '转化用户客单价']].head(10))
        else:
            print("未发现高潜力组合")

    def _generate_structured_combination_results(self, results, overall_rate):
        """生成结构化的组合分析结果"""
        # 计算阈值
        std_rate = results['转化率'].std()
        high_threshold = overall_rate + std_rate

        # 高转化率组合
        high_conversion = results[results['类型'] == '高转化率'].copy()
        high_conversion = high_conversion.sort_values('转化率', ascending=False)

        # 低转化率组合
        low_conversion = results[results['类型'] == '低转化率'].copy()
        low_conversion = low_conversion.sort_values('转化率', ascending=True)

        # 高潜力组合
        high_potential = results[
            (results['类型'] == '中等转化率') &
            (results['总人数'] >= results['总人数'].quantile(0.7)) &
            (results['转化率'] >= overall_rate)
        ].copy()
        high_potential = high_potential.sort_values(['总人数', '转化率'], ascending=[False, False])

        return {
            'high_conversion': high_conversion,
            'low_conversion': low_conversion,
            'high_potential': high_potential,
            'all_combinations': results.copy()
        }

    def export_combination_tables(self, combo_results):
        """导出组合分析表格到Excel和CSV文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        print(f"\n📊 正在导出组合分析表格...")

        # 1. 导出到Excel文件（多个工作表）
        excel_filename = f"组合分析详细数据_{timestamp}.xlsx"

        with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
            # 高转化率组合
            if len(combo_results['high_conversion']) > 0:
                high_conv_export = combo_results['high_conversion'].copy()
                high_conv_export['转化率'] = (high_conv_export['转化率'] * 100).round(2)
                high_conv_export['转化用户客单价'] = high_conv_export['转化用户客单价'].round(0)
                high_conv_export.to_excel(writer, sheet_name='高转化率组合', index=False)
                print(f"  ✅ 高转化率组合: {len(high_conv_export)} 条记录")

            # 高潜力组合
            if len(combo_results['high_potential']) > 0:
                high_potential_export = combo_results['high_potential'].copy()
                high_potential_export['转化率'] = (high_potential_export['转化率'] * 100).round(2)
                high_potential_export['转化用户客单价'] = high_potential_export['转化用户客单价'].round(0)
                high_potential_export.to_excel(writer, sheet_name='高潜力组合', index=False)
                print(f"  ✅ 高潜力组合: {len(high_potential_export)} 条记录")

            # 低转化率组合（前100条）
            if len(combo_results['low_conversion']) > 0:
                low_conv_export = combo_results['low_conversion'].head(100).copy()
                low_conv_export['转化率'] = (low_conv_export['转化率'] * 100).round(2)
                low_conv_export.to_excel(writer, sheet_name='低转化率组合_前100', index=False)
                print(f"  ✅ 低转化率组合: {len(low_conv_export)} 条记录（前100条）")

            # 所有组合汇总统计
            all_combos = combo_results['all_combinations']
            summary_stats = all_combos.groupby('维度数').agg({
                '总人数': ['count', 'mean', 'max', 'sum'],
                '转化率': ['mean', 'max', 'min'],
                '转化人数': 'sum',
                '转化用户客单价': 'mean'
            }).round(2)

            summary_stats.columns = ['组合数量', '平均样本量', '最大样本量', '总样本量',
                                   '平均转化率', '最高转化率', '最低转化率', '总转化人数', '平均客单价']
            summary_stats['平均转化率'] = (summary_stats['平均转化率'] * 100).round(2)
            summary_stats['最高转化率'] = (summary_stats['最高转化率'] * 100).round(2)
            summary_stats['最低转化率'] = (summary_stats['最低转化率'] * 100).round(2)

            summary_stats.to_excel(writer, sheet_name='维度汇总统计')
            print(f"  ✅ 维度汇总统计: {len(summary_stats)} 个维度")

        print(f"📁 Excel文件已保存: {excel_filename}")

        # 2. 导出高转化率组合到CSV（便于进一步分析）
        if len(combo_results['high_conversion']) > 0:
            csv_filename = f"高转化率组合_{timestamp}.csv"
            high_conv_csv = combo_results['high_conversion'].copy()
            high_conv_csv['转化率'] = (high_conv_csv['转化率'] * 100).round(2)
            high_conv_csv['转化用户客单价'] = high_conv_csv['转化用户客单价'].round(0)
            high_conv_csv.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            print(f"📁 CSV文件已保存: {csv_filename}")

        # 3. 导出高潜力组合到CSV
        if len(combo_results['high_potential']) > 0:
            csv_filename2 = f"高潜力组合_{timestamp}.csv"
            high_potential_csv = combo_results['high_potential'].copy()
            high_potential_csv['转化率'] = (high_potential_csv['转化率'] * 100).round(2)
            high_potential_csv['转化用户客单价'] = high_potential_csv['转化用户客单价'].round(0)
            high_potential_csv.to_csv(csv_filename2, index=False, encoding='utf-8-sig')
            print(f"📁 CSV文件已保存: {csv_filename2}")

        print(f"✅ 组合分析表格导出完成！")

    def generate_summary_report(self):
        """生成分析总结报告"""
        print("\n" + "="*80)
        print("📊 用户转化率分析总结报告")
        print("="*80)

        # 基础数据概览
        total_users = len(self.df)
        converted_users = len(self.df[self.df['is_converted'] == 1])
        overall_rate = converted_users / total_users * 100

        print(f"\n📈 基础数据概览:")
        print(f"   • 总用户数: {total_users:,}")
        print(f"   • 转化用户数: {converted_users:,}")
        print(f"   • 总转化率: {overall_rate:.2f}%")
        print(f"   • 转化用户平均客单价: {self.df[self.df['is_converted']==1][self.amount_col].mean():.2f}元")
        print(f"   • 所有用户平均金额: {self.df[self.amount_col].mean():.2f}元")

        # 各期转化率趋势
        print(f"\n📅 各期转化率趋势:")
        period_stats = self.df.groupby('🌝期数').agg({
            'is_converted': ['count', 'sum', 'mean']
        }).round(4)
        period_stats.columns = ['总人数', '转化人数', '转化率']
        period_stats['转化率'] = (period_stats['转化率'] * 100).round(2)

        # 显示转化率最高和最低的期数
        best_period = period_stats.loc[period_stats['转化率'].idxmax()]
        worst_period = period_stats.loc[period_stats['转化率'].idxmin()]

        print(f"   • 转化率最高: {best_period.name} ({best_period['转化率']:.2f}%)")
        print(f"   • 转化率最低: {worst_period.name} ({worst_period['转化率']:.2f}%)")

        # 单维度关键发现
        print(f"\n🎯 单维度关键发现:")

        # 工作阶段
        work_stage_stats = self.df.groupby('📗工作阶段')['is_converted'].agg(['count', 'sum', 'mean']).round(4)
        work_stage_stats.columns = ['总人数', '转化人数', '转化率']
        work_stage_stats = work_stage_stats.sort_values('转化率', ascending=False)
        work_stage_ranking = ' > '.join([f'{idx}({row["转化率"]*100:.1f}%)' for idx, row in work_stage_stats.iterrows()])
        print(f"   • 工作阶段转化率排序: {work_stage_ranking}")

        # 薪资区间
        salary_stats = self.df.groupby('📗当前薪资区间')['is_converted'].agg(['count', 'sum', 'mean']).round(4)
        salary_stats.columns = ['总人数', '转化人数', '转化率']
        salary_stats = salary_stats.sort_values('转化率', ascending=False)
        top_salary = salary_stats.head(3)
        salary_ranking = ', '.join([f'{idx}({row["转化率"]*100:.1f}%)' for idx, row in top_salary.iterrows()])
        print(f"   • 高转化率薪资区间: {salary_ranking}")

        # 投入成本
        cost_stats = self.df.groupby('📗在过去半年内，您在自我能力提升上投入了多少成本？')['is_converted'].agg(['count', 'sum', 'mean']).round(4)
        cost_stats.columns = ['总人数', '转化人数', '转化率']
        cost_stats = cost_stats.sort_values('转化率', ascending=False)
        top_cost = cost_stats.head(3)
        cost_ranking = ', '.join([f'{idx}({row["转化率"]*100:.1f}%)' for idx, row in top_cost.iterrows()])
        print(f"   • 高转化率投入成本: {cost_ranking}")

        print(f"\n💡 业务建议:")
        print(f"   1. 重点关注中层管理和基层管理用户，转化率相对较高")
        print(f"   2. 薪资25000以上用户群体转化率显著高于平均水平")
        print(f"   3. 已有投入成本的用户(3000元以上)转化意愿更强")
        print(f"   4. 一线和二线城市用户转化率相对稳定")
        print(f"   5. 建议针对高转化率标签组合制定精准营销策略")

def main():
    """主函数"""
    analyzer = ConversionAnalyzer('用户信息表.xlsx')

    # 加载数据
    df = analyzer.load_and_preprocess_data()

    # 1. 按期分析
    print("开始按期分析...")
    period_results = analyzer.analyze_by_period()

    # 2. 全期汇总分析
    print("\n" + "="*50)
    print("开始全期汇总分析...")
    overall_single = analyzer.analyze_single_dimension()

    # 3. 组合分析
    print("\n" + "="*50)
    print("开始组合分析...")
    overall_combo = analyzer.analyze_combinations()

    # 4. 导出组合分析表格
    if overall_combo is not None:
        analyzer.export_combination_tables(overall_combo)

    # 5. 生成总结报告
    analyzer.generate_summary_report()

    print("\n✅ 分析完成！")

if __name__ == "__main__":
    main()
