#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
转化率分析报告导出工具
将分析结果导出为Excel报告
"""

import pandas as pd
import numpy as np
from conversion_analysis import ConversionAnalyzer
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class ReportExporter:
    def __init__(self, analyzer):
        self.analyzer = analyzer
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def export_comprehensive_report(self, filename=None):
        """导出综合分析报告"""
        if filename is None:
            filename = f"转化率分析报告_{self.timestamp}.xlsx"
        
        print(f"正在生成综合报告: {filename}")
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 1. 数据概览
            self._export_overview(writer)
            
            # 2. 各期分析
            self._export_period_analysis(writer)
            
            # 3. 单维度分析
            self._export_single_dimension_analysis(writer)
            
            # 4. 组合分析
            self._export_combination_analysis(writer)
            
            # 5. 关键洞察
            self._export_key_insights(writer)
        
        print(f"✅ 报告已导出: {filename}")
        return filename
    
    def _export_overview(self, writer):
        """导出数据概览"""
        df = self.analyzer.df
        
        # 基础统计
        overview_data = {
            '指标': ['总用户数', '转化用户数', '总转化率(%)', '转化用户平均客单价(元)', '所有用户平均金额(元)'],
            '数值': [
                len(df),
                len(df[df['is_converted'] == 1]),
                len(df[df['is_converted'] == 1]) / len(df) * 100,
                df[df['is_converted']==1][self.analyzer.amount_col].mean(),
                df[self.analyzer.amount_col].mean()
            ]
        }
        overview_df = pd.DataFrame(overview_data)
        overview_df.to_excel(writer, sheet_name='数据概览', index=False)
        
        # 各期统计
        period_stats = df.groupby('🌝期数').agg({
            'is_converted': ['count', 'sum', 'mean'],
            self.analyzer.amount_col: ['mean', lambda x: x[x > self.analyzer.conversion_threshold].mean()]
        }).round(2)
        period_stats.columns = ['总人数', '转化人数', '转化率', '人均值', '转化用户客单价']
        period_stats['转化率'] = (period_stats['转化率'] * 100).round(2)
        
        period_stats.to_excel(writer, sheet_name='各期统计', startrow=0)
    
    def _export_period_analysis(self, writer):
        """导出各期详细分析"""
        periods = sorted(self.analyzer.df['🌝期数'].unique())
        
        for i, period in enumerate(periods):
            period_data = self.analyzer.df[self.analyzer.df['🌝期数'] == period]
            
            # 为每个维度创建分析
            all_results = []
            
            for dim_name, col_name in self.analyzer.dimensions.items():
                stats = period_data.groupby(col_name).agg({
                    'is_converted': ['count', 'sum', 'mean'],
                    self.analyzer.amount_col: ['mean', lambda x: x[x > self.analyzer.conversion_threshold].mean()]
                }).round(2)
                
                stats.columns = ['总人数', '转化人数', '转化率', '人均值', '转化用户客单价']
                stats['转化率'] = (stats['转化率'] * 100).round(2)
                stats['维度'] = dim_name
                stats['标签'] = stats.index
                stats = stats.reset_index(drop=True)
                
                all_results.append(stats)
            
            # 合并所有维度结果
            combined_results = pd.concat(all_results, ignore_index=True)
            combined_results = combined_results[['维度', '标签', '总人数', '转化人数', '转化率', '人均值', '转化用户客单价']]
            
            # 按转化率排序
            combined_results = combined_results.sort_values(['维度', '转化率'], ascending=[True, False])
            
            # 导出到Excel
            sheet_name = f'{period}分析'
            combined_results.to_excel(writer, sheet_name=sheet_name, index=False)
    
    def _export_single_dimension_analysis(self, writer):
        """导出单维度汇总分析"""
        df = self.analyzer.df
        
        all_results = []
        
        for dim_name, col_name in self.analyzer.dimensions.items():
            stats = df.groupby(col_name).agg({
                'is_converted': ['count', 'sum', 'mean'],
                self.analyzer.amount_col: ['mean', lambda x: x[x > self.analyzer.conversion_threshold].mean()]
            }).round(2)
            
            stats.columns = ['总人数', '转化人数', '转化率', '人均值', '转化用户客单价']
            stats['转化率'] = (stats['转化率'] * 100).round(2)
            stats['维度'] = dim_name
            stats['标签'] = stats.index
            stats = stats.reset_index(drop=True)
            
            all_results.append(stats)
        
        # 合并所有维度结果
        combined_results = pd.concat(all_results, ignore_index=True)
        combined_results = combined_results[['维度', '标签', '总人数', '转化人数', '转化率', '人均值', '转化用户客单价']]
        
        # 按转化率排序
        combined_results = combined_results.sort_values(['维度', '转化率'], ascending=[True, False])
        
        combined_results.to_excel(writer, sheet_name='单维度汇总', index=False)
    
    def _export_combination_analysis(self, writer):
        """导出组合分析"""
        # 这里简化处理，导出高潜力组合
        df = self.analyzer.df
        
        # 计算一些重要的二维组合
        important_combinations = [
            ('工作阶段', '薪资区间'),
            ('工作状态', '城市类别'),
            ('心力评分', '投入成本'),
            ('工作年限', '薪资区间')
        ]
        
        combo_results = []
        
        for dim1_name, dim2_name in important_combinations:
            col1 = self.analyzer.dimensions[dim1_name]
            col2 = self.analyzer.dimensions[dim2_name]
            
            combo_stats = df.groupby([col1, col2]).agg({
                'is_converted': ['count', 'sum', 'mean'],
                self.analyzer.amount_col: ['mean', lambda x: x[x > self.analyzer.conversion_threshold].mean()]
            }).round(4)
            
            combo_stats.columns = ['总人数', '转化人数', '转化率', '人均值', '转化用户客单价']
            
            # 过滤样本量太小的组合
            combo_stats = combo_stats[combo_stats['总人数'] >= 50]
            
            if len(combo_stats) > 0:
                combo_stats['转化率'] = (combo_stats['转化率'] * 100).round(2)
                combo_stats['组合'] = f"{dim1_name} × {dim2_name}"
                combo_stats['标签1'] = combo_stats.index.get_level_values(0)
                combo_stats['标签2'] = combo_stats.index.get_level_values(1)
                combo_stats = combo_stats.reset_index(drop=True)
                
                combo_results.append(combo_stats)
        
        if combo_results:
            all_combos = pd.concat(combo_results, ignore_index=True)
            all_combos = all_combos[['组合', '标签1', '标签2', '总人数', '转化人数', '转化率', '人均值', '转化用户客单价']]
            all_combos = all_combos.sort_values('转化率', ascending=False)
            
            all_combos.to_excel(writer, sheet_name='重要组合分析', index=False)
    
    def _export_key_insights(self, writer):
        """导出关键洞察"""
        df = self.analyzer.df
        
        insights = []
        
        # 1. 转化率最高的标签
        for dim_name, col_name in self.analyzer.dimensions.items():
            stats = df.groupby(col_name)['is_converted'].agg(['count', 'sum', 'mean']).round(4)
            stats.columns = ['总人数', '转化人数', '转化率']
            stats = stats[stats['总人数'] >= 100]  # 过滤样本量
            
            if len(stats) > 0:
                best_tag = stats.loc[stats['转化率'].idxmax()]
                insights.append({
                    '维度': dim_name,
                    '类型': '最高转化率标签',
                    '标签': best_tag.name,
                    '转化率(%)': best_tag['转化率'] * 100,
                    '样本量': best_tag['总人数'],
                    '转化人数': best_tag['转化人数']
                })
        
        # 2. 转化率最低的标签
        for dim_name, col_name in self.analyzer.dimensions.items():
            stats = df.groupby(col_name)['is_converted'].agg(['count', 'sum', 'mean']).round(4)
            stats.columns = ['总人数', '转化人数', '转化率']
            stats = stats[stats['总人数'] >= 100]  # 过滤样本量
            
            if len(stats) > 0:
                worst_tag = stats.loc[stats['转化率'].idxmin()]
                insights.append({
                    '维度': dim_name,
                    '类型': '最低转化率标签',
                    '标签': worst_tag.name,
                    '转化率(%)': worst_tag['转化率'] * 100,
                    '样本量': worst_tag['总人数'],
                    '转化人数': worst_tag['转化人数']
                })
        
        insights_df = pd.DataFrame(insights)
        insights_df.to_excel(writer, sheet_name='关键洞察', index=False)

def main():
    """主函数"""
    # 创建分析器
    analyzer = ConversionAnalyzer('用户信息表.xlsx')
    analyzer.load_and_preprocess_data()
    
    # 创建报告导出器
    exporter = ReportExporter(analyzer)
    
    # 导出综合报告
    filename = exporter.export_comprehensive_report()
    
    print(f"\n📊 报告导出完成!")
    print(f"文件名: {filename}")
    print(f"包含内容:")
    print(f"  • 数据概览")
    print(f"  • 各期详细分析")
    print(f"  • 单维度汇总分析")
    print(f"  • 重要组合分析")
    print(f"  • 关键洞察")

if __name__ == "__main__":
    main()
