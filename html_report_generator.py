#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML可视化报告生成器
生成交互式的转化率分析报告
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo
from conversion_analysis import ConversionAnalyzer
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class HTMLReportGenerator:
    def __init__(self, analyzer):
        self.analyzer = analyzer
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def generate_html_report(self, filename=None):
        """生成完整的HTML可视化报告"""
        if filename is None:
            filename = f"转化率分析可视化报告_{self.timestamp}.html"
        
        print(f"正在生成HTML可视化报告: {filename}")
        
        # 生成各种图表
        charts = {
            'overview': self._create_overview_charts(),
            'period_trend': self._create_period_trend_chart(),
            'dimension_analysis': self._create_dimension_analysis_charts(),
            'combination_analysis': self._create_combination_analysis_chart(),
            'conversion_funnel': self._create_conversion_funnel_chart(),
            'combination_tables': self._create_combination_tables()
        }
        
        # 生成HTML内容
        html_content = self._generate_html_template(charts)
        
        # 保存文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ HTML报告已生成: {filename}")
        return filename
    
    def _create_overview_charts(self):
        """创建概览图表"""
        df = self.analyzer.df
        
        # 基础数据统计
        total_users = len(df)
        converted_users = len(df[df['is_converted'] == 1])
        conversion_rate = converted_users / total_users * 100
        
        # 转化率饼图
        pie_fig = go.Figure(data=[go.Pie(
            labels=['已转化', '未转化'],
            values=[converted_users, total_users - converted_users],
            hole=0.4,
            marker_colors=['#2E8B57', '#DC143C']
        )])
        pie_fig.update_layout(
            title=f"总体转化率: {conversion_rate:.2f}%",
            font=dict(size=14),
            height=400
        )
        
        # 转化用户金额分布
        converted_amounts = df[df['is_converted'] == 1][self.analyzer.amount_col]
        hist_fig = px.histogram(
            x=converted_amounts,
            nbins=30,
            title="转化用户金额分布",
            labels={'x': '转化金额(元)', 'y': '用户数量'}
        )
        hist_fig.update_layout(height=400)
        
        return {
            'pie_chart': pie_fig.to_html(include_plotlyjs=False, div_id="overview_pie"),
            'histogram': hist_fig.to_html(include_plotlyjs=False, div_id="amount_hist")
        }
    
    def _create_period_trend_chart(self):
        """创建各期趋势图表"""
        df = self.analyzer.df
        
        # 计算各期统计
        period_stats = df.groupby('🌝期数').agg({
            'is_converted': ['count', 'sum', 'mean'],
            self.analyzer.amount_col: ['mean', lambda x: x[x > self.analyzer.conversion_threshold].mean()]
        }).round(4)
        
        period_stats.columns = ['总人数', '转化人数', '转化率', '人均值', '转化用户客单价']
        period_stats['转化率'] = period_stats['转化率'] * 100
        
        # 创建双轴图表
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('各期转化率趋势', '各期用户数量', '各期转化人数', '各期客单价'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # 转化率趋势
        fig.add_trace(
            go.Scatter(x=period_stats.index, y=period_stats['转化率'],
                      mode='lines+markers', name='转化率(%)',
                      line=dict(color='#2E8B57', width=3)),
            row=1, col=1
        )
        
        # 用户数量
        fig.add_trace(
            go.Bar(x=period_stats.index, y=period_stats['总人数'],
                   name='总人数', marker_color='#4682B4'),
            row=1, col=2
        )
        
        # 转化人数
        fig.add_trace(
            go.Bar(x=period_stats.index, y=period_stats['转化人数'],
                   name='转化人数', marker_color='#FF6347'),
            row=2, col=1
        )
        
        # 客单价
        fig.add_trace(
            go.Scatter(x=period_stats.index, y=period_stats['转化用户客单价'],
                      mode='lines+markers', name='客单价(元)',
                      line=dict(color='#9370DB', width=3)),
            row=2, col=2
        )
        
        fig.update_layout(height=600, showlegend=False, title_text="各期数据趋势分析")
        
        return fig.to_html(include_plotlyjs=False, div_id="period_trend")
    
    def _create_dimension_analysis_charts(self):
        """创建各维度分析图表"""
        df = self.analyzer.df
        charts = {}
        
        for dim_name, col_name in self.analyzer.dimensions.items():
            # 计算该维度的转化率
            stats = df.groupby(col_name).agg({
                'is_converted': ['count', 'sum', 'mean'],
                self.analyzer.amount_col: lambda x: x[x > self.analyzer.conversion_threshold].mean()
            }).round(4)
            
            stats.columns = ['总人数', '转化人数', '转化率', '转化用户客单价']
            stats['转化率'] = stats['转化率'] * 100
            stats = stats.sort_values('转化率', ascending=True)
            
            # 创建水平条形图
            fig = go.Figure()
            
            # 添加转化率条形图
            fig.add_trace(go.Bar(
                y=stats.index,
                x=stats['转化率'],
                orientation='h',
                name='转化率(%)',
                text=[f'{rate:.1f}%' for rate in stats['转化率']],
                textposition='outside',
                marker_color='#2E8B57'
            ))
            
            fig.update_layout(
                title=f"{dim_name} - 转化率分析",
                xaxis_title="转化率(%)",
                yaxis_title=dim_name,
                height=max(400, len(stats) * 40),
                margin=dict(l=150)
            )
            
            charts[dim_name] = fig.to_html(include_plotlyjs=False, div_id=f"dim_{dim_name}")
        
        return charts
    
    def _create_combination_analysis_chart(self):
        """创建组合分析图表"""
        print("正在进行多维组合分析...")

        # 运行组合分析
        combo_results = self.analyzer.analyze_combinations(min_sample_size=50)

        if combo_results is None or len(combo_results.get('all_combinations', pd.DataFrame())) == 0:
            return "<p>暂无组合分析数据</p>"

        all_combos = combo_results['all_combinations']
        high_conv = combo_results['high_conversion']
        high_potential = combo_results['high_potential']

        # 创建多个子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('高转化率组合分析', '高潜力组合分析', '各维度组合数量', '转化率分布'),
            specs=[[{"type": "scatter"}, {"type": "scatter"}],
                   [{"type": "bar"}, {"type": "histogram"}]]
        )

        # 1. 高转化率组合气泡图
        if len(high_conv) > 0:
            fig.add_trace(
                go.Scatter(
                    x=high_conv['总人数'],
                    y=high_conv['转化率'] * 100,
                    mode='markers',
                    marker=dict(
                        size=high_conv['转化人数'] * 2,
                        color=high_conv['维度数'],
                        colorscale='Viridis',
                        showscale=True,
                        colorbar=dict(title="维度数")
                    ),
                    text=high_conv['组合名称'],
                    name='高转化率组合',
                    hovertemplate='<b>%{text}</b><br>样本量: %{x}<br>转化率: %{y:.1f}%<extra></extra>'
                ),
                row=1, col=1
            )

        # 2. 高潜力组合散点图
        if len(high_potential) > 0:
            fig.add_trace(
                go.Scatter(
                    x=high_potential['总人数'],
                    y=high_potential['转化率'] * 100,
                    mode='markers',
                    marker=dict(
                        size=8,
                        color='orange',
                        opacity=0.7
                    ),
                    text=high_potential['组合名称'],
                    name='高潜力组合',
                    hovertemplate='<b>%{text}</b><br>样本量: %{x}<br>转化率: %{y:.1f}%<extra></extra>'
                ),
                row=1, col=2
            )

        # 3. 各维度组合数量统计
        dimension_counts = all_combos['维度数'].value_counts().sort_index()
        fig.add_trace(
            go.Bar(
                x=[f'{i}维组合' for i in dimension_counts.index],
                y=dimension_counts.values,
                name='组合数量',
                marker_color='lightblue'
            ),
            row=2, col=1
        )

        # 4. 转化率分布直方图
        fig.add_trace(
            go.Histogram(
                x=all_combos['转化率'] * 100,
                nbinsx=20,
                name='转化率分布',
                marker_color='lightgreen'
            ),
            row=2, col=2
        )

        # 更新布局
        fig.update_layout(
            height=800,
            showlegend=False,
            title_text="多维组合分析总览"
        )

        # 更新坐标轴标签
        fig.update_xaxes(title_text="样本量", row=1, col=1)
        fig.update_yaxes(title_text="转化率(%)", row=1, col=1)
        fig.update_xaxes(title_text="样本量", row=1, col=2)
        fig.update_yaxes(title_text="转化率(%)", row=1, col=2)
        fig.update_xaxes(title_text="维度类型", row=2, col=1)
        fig.update_yaxes(title_text="组合数量", row=2, col=1)
        fig.update_xaxes(title_text="转化率(%)", row=2, col=2)
        fig.update_yaxes(title_text="频次", row=2, col=2)

        return fig.to_html(include_plotlyjs=False, div_id="combination_analysis")
    
    def _create_conversion_funnel_chart(self):
        """创建转化漏斗图"""
        df = self.analyzer.df
        
        # 按薪资区间创建漏斗
        salary_stats = df.groupby('📗当前薪资区间').agg({
            'is_converted': ['count', 'sum']
        }).round(0)
        
        salary_stats.columns = ['总人数', '转化人数']
        salary_stats = salary_stats.sort_values('总人数', ascending=False)
        
        fig = go.Figure(go.Funnel(
            y=salary_stats.index,
            x=salary_stats['总人数'],
            textinfo="value+percent initial",
            marker_color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']
        ))
        
        fig.update_layout(
            title="薪资区间用户分布漏斗图",
            height=500
        )
        
        return fig.to_html(include_plotlyjs=False, div_id="conversion_funnel")

    def _create_combination_tables(self):
        """创建组合分析数据表"""
        print("正在生成组合分析数据表...")

        # 运行组合分析
        combo_results = self.analyzer.analyze_combinations(min_sample_size=30)

        if combo_results is None or len(combo_results.get('all_combinations', pd.DataFrame())) == 0:
            return {
                'high_conversion_table': "<p>暂无高转化率组合数据</p>",
                'high_potential_table': "<p>暂无高潜力组合数据</p>",
                'dimension_summary': "<p>暂无维度汇总数据</p>"
            }

        high_conv = combo_results['high_conversion']
        high_potential = combo_results['high_potential']
        all_combos = combo_results['all_combinations']

        # 1. 高转化率组合表格
        if len(high_conv) > 0:
            high_conv_display = high_conv.head(10).copy()
            high_conv_display['转化率'] = (high_conv_display['转化率'] * 100).round(2)
            high_conv_display['转化用户客单价'] = high_conv_display['转化用户客单价'].round(0)

            high_conv_table = high_conv_display[['组合名称', '维度数', '总人数', '转化人数', '转化率', '转化用户客单价']].to_html(
                classes='table table-striped table-hover',
                table_id='high_conversion_table',
                escape=False,
                index=False
            )
        else:
            high_conv_table = "<p>未发现高转化率组合</p>"

        # 2. 高潜力组合表格
        if len(high_potential) > 0:
            high_potential_display = high_potential.head(15).copy()
            high_potential_display['转化率'] = (high_potential_display['转化率'] * 100).round(2)
            high_potential_display['转化用户客单价'] = high_potential_display['转化用户客单价'].round(0)

            high_potential_table = high_potential_display[['组合名称', '维度数', '总人数', '转化人数', '转化率', '转化用户客单价']].to_html(
                classes='table table-striped table-hover',
                table_id='high_potential_table',
                escape=False,
                index=False
            )
        else:
            high_potential_table = "<p>未发现高潜力组合</p>"

        # 3. 维度汇总统计
        dimension_summary = all_combos.groupby('维度数').agg({
            '总人数': ['count', 'mean', 'max'],
            '转化率': ['mean', 'max'],
            '转化人数': 'sum'
        }).round(2)

        dimension_summary.columns = ['组合数量', '平均样本量', '最大样本量', '平均转化率', '最高转化率', '总转化人数']
        dimension_summary['平均转化率'] = (dimension_summary['平均转化率'] * 100).round(2)
        dimension_summary['最高转化率'] = (dimension_summary['最高转化率'] * 100).round(2)

        dimension_summary_table = dimension_summary.to_html(
            classes='table table-striped table-hover',
            table_id='dimension_summary_table'
        )

        return {
            'high_conversion_table': high_conv_table,
            'high_potential_table': high_potential_table,
            'dimension_summary': dimension_summary_table
        }
    
    def _generate_html_template(self, charts):
        """生成HTML模板"""
        # 获取基础统计数据
        df = self.analyzer.df
        total_users = len(df)
        converted_users = len(df[df['is_converted'] == 1])
        conversion_rate = converted_users / total_users * 100
        avg_amount = df[df['is_converted']==1][self.analyzer.amount_col].mean()
        
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户转化率分析可视化报告</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #2E8B57;
        }}
        .header h1 {{
            color: #2E8B57;
            margin-bottom: 10px;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }}
        .stat-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .stat-label {{
            font-size: 0.9em;
            opacity: 0.9;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .section h2 {{
            color: #333;
            border-left: 4px solid #2E8B57;
            padding-left: 15px;
            margin-bottom: 20px;
        }}
        .chart-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .chart-container {{
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .dimension-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }}
        .table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }}
        .table th, .table td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        .table th {{
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }}
        .table-striped tbody tr:nth-child(odd) {{
            background-color: #f9f9f9;
        }}
        .table-hover tbody tr:hover {{
            background-color: #f5f5f5;
        }}
        .table-container {{
            overflow-x: auto;
            margin: 20px 0;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 用户转化率分析可视化报告</h1>
            <p>生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{total_users:,}</div>
                <div class="stat-label">总用户数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{converted_users:,}</div>
                <div class="stat-label">转化用户数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{conversion_rate:.2f}%</div>
                <div class="stat-label">总转化率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">¥{avg_amount:,.0f}</div>
                <div class="stat-label">平均客单价</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 数据概览</h2>
            <div class="chart-grid">
                <div class="chart-container">
                    {charts['overview']['pie_chart']}
                </div>
                <div class="chart-container">
                    {charts['overview']['histogram']}
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📅 各期趋势分析</h2>
            <div class="chart-container">
                {charts['period_trend']}
            </div>
        </div>
        
        <div class="section">
            <h2>🎯 各维度转化率分析</h2>
            <div class="dimension-grid">
"""
        
        # 添加各维度图表
        for dim_name, chart_html in charts['dimension_analysis'].items():
            html_template += f"""
                <div class="chart-container">
                    {chart_html}
                </div>
"""
        
        html_template += f"""
            </div>
        </div>
        
        <div class="section">
            <h2>🔍 多维组合分析</h2>
            <div class="chart-container">
                {charts['combination_analysis']}
            </div>

            <h3>📊 高转化率组合详情</h3>
            <div class="table-container">
                {charts['combination_tables']['high_conversion_table']}
            </div>

            <h3>🎯 高潜力组合详情</h3>
            <div class="table-container">
                {charts['combination_tables']['high_potential_table']}
            </div>

            <h3>📈 各维度组合汇总</h3>
            <div class="table-container">
                {charts['combination_tables']['dimension_summary']}
            </div>
        </div>
        
        <div class="section">
            <h2>📊 用户分布漏斗</h2>
            <div class="chart-container">
                {charts['conversion_funnel']}
            </div>
        </div>
        
        <div class="section">
            <h2>💡 关键洞察</h2>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #2E8B57;">
                <ul style="line-height: 1.8;">
                    <li><strong>总体表现:</strong> 转化率为 {conversion_rate:.2f}%，转化用户平均客单价 ¥{avg_amount:,.0f}</li>
                    <li><strong>高价值用户:</strong> 薪资25000+用户群体转化率显著高于平均水平</li>
                    <li><strong>投入意愿:</strong> 已有学习投入成本的用户转化意愿更强</li>
                    <li><strong>职业特征:</strong> 基层管理和中层管理用户转化率相对较高</li>
                    <li><strong>地域分布:</strong> 一线、二线城市用户转化率相对稳定</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
"""
        
        return html_template

def main():
    """主函数"""
    # 创建分析器
    analyzer = ConversionAnalyzer('用户信息表.xlsx')
    analyzer.load_and_preprocess_data()
    
    # 创建HTML报告生成器
    generator = HTMLReportGenerator(analyzer)
    
    # 生成HTML报告
    filename = generator.generate_html_report()
    
    print(f"\n🎉 HTML可视化报告生成完成!")
    print(f"文件名: {filename}")
    print(f"包含内容:")
    print(f"  • 数据概览图表")
    print(f"  • 各期趋势分析")
    print(f"  • 各维度转化率分析")
    print(f"  • 组合分析可视化")
    print(f"  • 用户分布漏斗图")
    print(f"  • 关键洞察总结")

if __name__ == "__main__":
    main()
