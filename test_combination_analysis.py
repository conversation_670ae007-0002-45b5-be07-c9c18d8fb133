#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试组合分析功能
"""

from conversion_analysis import ConversionAnalyzer
import pandas as pd

def test_combination_analysis():
    """测试组合分析功能"""
    print("=== 测试组合分析功能 ===")
    
    # 创建分析器
    analyzer = ConversionAnalyzer('用户信息表.xlsx')
    analyzer.load_and_preprocess_data()
    
    # 运行组合分析
    print("\n正在运行组合分析...")
    combo_results = analyzer.analyze_combinations(min_sample_size=50)
    
    if combo_results is None:
        print("❌ 组合分析返回None")
        return
    
    print(f"\n✅ 组合分析成功返回结构化数据")
    print(f"返回数据类型: {type(combo_results)}")
    print(f"包含的键: {list(combo_results.keys())}")
    
    # 检查各部分数据
    for key, data in combo_results.items():
        if isinstance(data, pd.DataFrame):
            print(f"\n📊 {key}:")
            print(f"  - 数据类型: DataFrame")
            print(f"  - 行数: {len(data)}")
            print(f"  - 列数: {len(data.columns) if len(data) > 0 else 0}")
            if len(data) > 0:
                print(f"  - 列名: {list(data.columns)}")
                print(f"  - 前3行预览:")
                print(data.head(3)[['组合名称', '维度数', '总人数', '转化人数', '转化率']].to_string(index=False))
        else:
            print(f"\n📊 {key}: {type(data)}")
    
    # 测试高转化率组合
    high_conv = combo_results['high_conversion']
    if len(high_conv) > 0:
        print(f"\n🎯 发现 {len(high_conv)} 个高转化率组合:")
        for i, row in high_conv.head(5).iterrows():
            print(f"  {row['组合名称']}: {row['转化率']:.1%} ({row['总人数']}人)")
    
    # 测试高潜力组合
    high_potential = combo_results['high_potential']
    if len(high_potential) > 0:
        print(f"\n💎 发现 {len(high_potential)} 个高潜力组合:")
        for i, row in high_potential.head(5).iterrows():
            print(f"  {row['组合名称']}: {row['转化率']:.1%} ({row['总人数']}人)")
    
    print(f"\n✅ 测试完成！组合分析功能正常工作")

if __name__ == "__main__":
    test_combination_analysis()
